# PosOrderReportData 千位符格式化功能

## 实现概述

为 `PosOrderReportData` 类添加了千位符格式化功能，确保 price 和 amount 字段在输出时使用千位符分隔，同时不丢失任何精度。

## 主要更改

### 1. 新增字段
- `amountDisplay` (String): 格式化后的数量显示
- `priceDisplay` (String): 格式化后的价格显示

### 2. 格式化方法
```java
private static String formatBigDecimalForReport(BigDecimal value) {
    if (value == null) {
        return "";
    }
    
    // stripTrailingZeros() 末尾0除去
    // toPlainString() 指数表記にならないようにString変換
    String strValue = value.stripTrailingZeros().toPlainString();
    
    // 整数部3桁区切り
    int decimalPointIndex = strValue.indexOf(".");
    if (decimalPointIndex < 0) {
        // 小数点なし
        return NUMBER_FORMAT.format(Long.valueOf(strValue));
    } else {
        // 小数点あり
        String seisu = strValue.substring(0, decimalPointIndex);
        return NUMBER_FORMAT.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
    }
}
```

### 3. 更新的 setProperties 方法
在设置 amount 和 price 时，同时设置对应的格式化显示字段：

```java
// Set amount and formatted amount display
BigDecimal scaledAmount = currencyPair.getOperateScaledAmount(posOrder.getAmount());
this.setAmount(scaledAmount);
this.setAmountDisplay(formatBigDecimalForReport(scaledAmount));

// Set price and formatted price display
BigDecimal scaledPrice = currencyPair.getScaledPrice(posOrder.getPrice());
this.setPrice(scaledPrice);
this.setPriceDisplay(formatBigDecimalForReport(scaledPrice));
```

### 4. 更新的报表头部
添加了显示字段的标题：
- "数量（表示用）" 
- "价格（表示用）"

## 功能特点

1. **保持精度**: 原始的 BigDecimal 字段 (`amount`, `price`) 保持不变，确保数值精度不丢失
2. **千位符格式化**: 新的显示字段使用千位符分隔，提高可读性
3. **去除尾随零**: 使用 `stripTrailingZeros()` 去除不必要的尾随零
4. **避免科学计数法**: 使用 `toPlainString()` 确保不使用科学计数法表示
5. **向后兼容**: 保留原有字段，不影响现有功能

## 示例输出

| 原始值 | 格式化后 |
|--------|----------|
| 12345.6789 | 12,345.6789 |
| 1234567.89 | 1,234,567.89 |
| 1000.0000 | 1,000 |
| 0.123456 | 0.123456 |

## JSON 输出示例

```json
{
  "id": 1,
  "userId": 12345,
  "currencyPair": "BTC_JPY",
  "amount": 12345.6789,
  "amountDisplay": "12,345.6789",
  "orderSide": "買い",
  "orderType": "指値",
  "price": 1234567.89,
  "priceDisplay": "1,234,567.89",
  "remainingAmountDisplay": "5,432.1",
  "orderStatus": "未約定",
  "orderChannel": "WEB",
  "createdAt": "2024/01/15 10:30:00",
  "updatedAt": "2024/01/15 10:30:00",
  "tradeType": "投資",
  "notes": "Test order"
}
```

## 使用方法

客户端可以选择使用：
- `amount` / `price`: 用于计算和精确数值处理
- `amountDisplay` / `priceDisplay`: 用于用户界面显示

这样既保证了数据的精确性，又提供了用户友好的显示格式。
