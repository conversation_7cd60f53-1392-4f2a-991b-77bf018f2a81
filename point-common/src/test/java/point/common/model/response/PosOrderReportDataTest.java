package point.common.model.response;

import static org.junit.jupiter.api.Assertions.*;

import java.math.BigDecimal;
import java.util.Date;
import org.junit.jupiter.api.Test;
import point.common.constant.CurrencyPair;
import point.common.constant.UserIdType;
import point.pos.entity.PosOrder;
import point.pos.entity.PosOrder.OrderChannel;
import point.pos.entity.PosOrder.OrderSide;
import point.pos.entity.PosOrder.OrderStatus;
import point.pos.entity.PosOrder.OrderType;

class PosOrderReportDataTest {

    @Test
    void testThousandSeparatorFormatting() {
        // Create a mock PosOrder
        PosOrder posOrder = new PosOrder();
        posOrder.setId(1L);
        posOrder.setUserId(12345L);
        posOrder.setAmount(new BigDecimal("12345.6789"));
        posOrder.setPrice(new BigDecimal("1234567.89"));
        posOrder.setRemainingAmount(new BigDecimal("5432.1"));
        posOrder.setOrderSide(OrderSide.BUY);
        posOrder.setOrderType(OrderType.LIMIT);
        posOrder.setOrderChannel(OrderChannel.WEB);
        posOrder.setOrderStatus(OrderStatus.UNFILLED);
        posOrder.setIdType(UserIdType.Invest);
        posOrder.setCreatedAt(new Date());
        posOrder.setUpdatedAt(new Date());
        posOrder.setNotes("Test order");

        // Create PosOrderReportData and set properties
        PosOrderReportData reportData = new PosOrderReportData();
        reportData.setProperties(posOrder, CurrencyPair.BTC_JPY);

        // Verify that amount and price are formatted with thousand separators
        assertNotNull(reportData.getAmountDisplay());
        assertNotNull(reportData.getPriceDisplay());

        // The exact format depends on the locale, but should contain commas for thousand separators
        // For Japanese locale, it should be like "12,345.6789"
        assertTrue(
                reportData.getAmountDisplay().contains(","),
                "Amount display should contain thousand separator: "
                        + reportData.getAmountDisplay());
        assertTrue(
                reportData.getPriceDisplay().contains(","),
                "Price display should contain thousand separator: " + reportData.getPriceDisplay());

        // Verify that original BigDecimal values are preserved
        assertNotNull(reportData.getAmount());
        assertNotNull(reportData.getPrice());

        // Verify that remaining amount display is also formatted
        assertTrue(
                reportData.getRemainingAmountDisplay().contains(","),
                "Remaining amount display should contain thousand separator: "
                        + reportData.getRemainingAmountDisplay());

        System.out.println("Amount: " + reportData.getAmount());
        System.out.println("Amount Display: " + reportData.getAmountDisplay());
        System.out.println("Price: " + reportData.getPrice());
        System.out.println("Price Display: " + reportData.getPriceDisplay());
        System.out.println("Remaining Amount Display: " + reportData.getRemainingAmountDisplay());
    }

    @Test
    void testZeroRemainingAmount() {
        // Create a mock PosOrder with zero remaining amount
        PosOrder posOrder = new PosOrder();
        posOrder.setId(1L);
        posOrder.setUserId(12345L);
        posOrder.setAmount(new BigDecimal("1000"));
        posOrder.setPrice(new BigDecimal("50000"));
        posOrder.setRemainingAmount(BigDecimal.ZERO);
        posOrder.setOrderSide(OrderSide.BUY);
        posOrder.setOrderType(OrderType.LIMIT);
        posOrder.setOrderChannel(OrderChannel.WEB);
        posOrder.setOrderStatus(OrderStatus.FULLY_FILLED);
        posOrder.setIdType(UserIdType.Invest);
        posOrder.setCreatedAt(new Date());
        posOrder.setUpdatedAt(new Date());

        // Create PosOrderReportData and set properties
        PosOrderReportData reportData = new PosOrderReportData();
        reportData.setProperties(posOrder, CurrencyPair.BTC_JPY);

        // Verify that remaining amount display shows "-" for zero
        assertEquals("-", reportData.getRemainingAmountDisplay());
    }

    @Test
    void testReportHeader() {
        String header = PosOrderReportData.getReportHeader();

        // Verify that header contains the display fields
        assertTrue(header.contains("数量（表示用）"), "Header should contain amount display field");
        assertTrue(header.contains("価格（表示用）"), "Header should contain price display field");

        System.out.println("Report Header: " + header);
    }
}
