package point.common.model.response;

import static point.common.util.StringUtil.formatBigDecimalForReport;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.CurrencyPair;
import point.common.constant.PosConstants;
import point.common.constant.ReportLabel;
import point.common.constant.ReportLabel.HeaderOrder;
import point.common.constant.UserIdType;
import point.common.util.CurrencyUtils;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;
import point.pos.entity.PosOrder;

@NoArgsConstructor
@JsonIgnoreProperties(
        ignoreUnknown = true,
        value = {"remainingAmount"})
@Getter
@Setter
@JsonPropertyOrder({
    "id",
    "userId",
    "currencyPair",
    "amount",
    "orderSide",
    "orderType",
    "price",
    "remainingAmountDisplay",
    "orderStatus",
    "orderChannel",
    "createdAt",
    "updatedAt",
    "tradeType",
    "notes"
})
public class PosOrderReportData implements Serializable {
    private static final long serialVersionUID = 6738398108710388974L;

    private Long id;
    private Long userId;
    private String currencyPair;
    private BigDecimal amount;
    private String orderSide;
    private String orderType;
    private String price;
    private BigDecimal remainingAmount;
    private String remainingAmountDisplay;
    private String orderStatus;
    private String orderChannel;
    private String createdAt;
    private String updatedAt;
    private String tradeType;
    private String notes;

    public PosOrderReportData setProperties(PosOrder posOrder, CurrencyPair currencyPair) {
        this.setUserId(posOrder.getUserId());
        this.setId(posOrder.getId());
        this.setAmount(currencyPair.getOperateScaledAmount(posOrder.getAmount()));
        this.setOrderSide(
                ReportLabel.OrderSide.valueOfName(posOrder.getOrderSide().name()).getLabel());
        this.setOrderType(
                ReportLabel.OrderType.valueOfName(posOrder.getOrderType().name()).getLabel());
        this.setOrderChannel(
                ReportLabel.OrderChannel.valueOfName(posOrder.getOrderChannel().name()).getLabel());
        this.setPrice(formatBigDecimalForReport(currencyPair.getScaledPrice(posOrder.getPrice())));
        this.setRemainingAmountDisplay(
                BigDecimal.ZERO.compareTo(posOrder.getRemainingAmount()) == 0
                        ? "-"
                        : currencyPair
                                .getOperateScaledAmount(posOrder.getRemainingAmount())
                                .toString());
        this.setOrderStatus(
                ReportLabel.PosOrderStatus.valueOfName(posOrder.getOrderStatus().name())
                        .getLabel());
        this.setCreatedAt(
                FormatUtil.formatJst(
                        posOrder.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
        this.setUpdatedAt(
                FormatUtil.formatJst(
                        posOrder.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
        if (UserIdType.Operate.equals(posOrder.getIdType())) {
            this.setTradeType(PosConstants.OPERATE_TRADE_TYPE_NAME);
            CurrencyUtils.setCurrencyPairToEntity(this::setCurrencyPair, currencyPair);
        } else {
            this.setTradeType(PosConstants.INVEST_TRADE_TYPE_NAME);
            this.setCurrencyPair(currencyPair.getName());
        }
        this.setNotes(posOrder.getNotes());
        return this;
    }

    public static String getReportHeader() {
        return String.join(
                ",",
                HeaderOrder.ID.getLabel(),
                HeaderOrder.USER_ID.getLabel(),
                HeaderOrder.CURRENCY_PAIR.getLabel(),
                HeaderOrder.AMOUNT.getLabel(),
                HeaderOrder.ORDER_SIDE.getLabel(),
                HeaderOrder.ORDER_TYPE.getLabel(),
                HeaderOrder.PRICE.getLabel(),
                HeaderOrder.REMAINING_AMOUNT.getLabel(),
                HeaderOrder.ORDER_STATUS.getLabel(),
                HeaderOrder.ORDER_CHANNEL.getLabel(),
                HeaderOrder.CREATED_AT.getLabel(),
                HeaderOrder.UPDATED_AT.getLabel(),
                HeaderOrder.TRADE_TYPE.getLabel(),
                HeaderOrder.NOTES.getLabel());
    }
}
