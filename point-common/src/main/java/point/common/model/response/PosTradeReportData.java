package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.NumberFormat;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.*;
import point.common.constant.ReportLabel.HeaderTrade;
import point.common.util.CurrencyUtils;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;
import point.pos.entity.PosTrade;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@JsonPropertyOrder({
    "id",
    "userId",
    "orderId",
    "currencyPair",
    "amount",
    "orderSide",
    "orderType",
    "tradeAction",
    "orderChannel",
    "price",
    "fee",
    "assetAmount",
    "createdAt",
    "updatedAt",
    "tradeType"
})
public class PosTradeReportData implements Serializable {

    private static final long serialVersionUID = -2885403527974338834L;

    private Long id;

    private Long userId;

    private Long orderId;

    private String currencyPair;

    private String amount;

    private String orderSide;

    private String orderType;

    private String tradeAction;

    private String orderChannel = OrderChannel.UNKNOWN.name();

    private String price;

    private String fee;

    private String assetAmount;

    private String createdAt;

    private String updatedAt;

    private String tradeType;

    public PosTradeReportData setProperties(PosTrade posTrade, CurrencyPair currencyPair) {
        this.setId(posTrade.getId());
        this.setUserId(posTrade.getUserId());
        this.setOrderId(posTrade.getOrderId());
        this.setOrderSide(
                ReportLabel.OrderSide.valueOfName(posTrade.getOrderSide().name()).getLabel());
        this.setOrderType(
                ReportLabel.OrderType.valueOfName(posTrade.getOrderType().name()).getLabel());
        this.setTradeAction(
                ReportLabel.TradeAction.valueOfName(posTrade.getTradeAction().name()).getLabel());
        this.setOrderChannel(
                ReportLabel.OrderChannel.valueOfName(posTrade.getOrderChannel().name()).getLabel());
        BigDecimal scaledPrice = currencyPair.getScaledPrice(posTrade.getPrice());
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        numberFormat.setGroupingUsed(true);
        numberFormat.setMaximumFractionDigits(scaledPrice.scale());
        this.setPrice(numberFormat.format(scaledPrice));
        this.setFee(currencyPair.getScaledPrice(posTrade.getFee()).toPlainString());
        this.setAssetAmount(
                currencyPair.getPosScaledAmount(posTrade.getAssetAmount()).toPlainString());
        this.setCreatedAt(
                FormatUtil.formatJst(
                        posTrade.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
        this.setUpdatedAt(
                FormatUtil.formatJst(
                        posTrade.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_SLASH));
        if (UserIdType.Operate.equals(posTrade.getIdType())) {
            this.setTradeType(PosConstants.OPERATE_TRADE_TYPE_NAME);
            this.setAmount(
                    currencyPair
                            .getAmountByTradeType(posTrade.getAmount(), TradeType.OPERATE)
                            .toPlainString());
            CurrencyUtils.setCurrencyPairToEntity(this::setCurrencyPair, currencyPair);
        } else {
            this.setTradeType(PosConstants.INVEST_TRADE_TYPE_NAME);
            this.setAmount(
                    currencyPair
                            .getAmountByTradeType(posTrade.getAmount(), TradeType.INVEST)
                            .toPlainString());
            this.setCurrencyPair(currencyPair.getName());
        }
        return this;
    }

    public static String getReportHeader() {
        return HeaderTrade.ID.getLabel()
                + ","
                + HeaderTrade.USER_ID.getLabel()
                + ","
                + HeaderTrade.ORDER_ID.getLabel()
                + ","
                + HeaderTrade.CURRENCY_PAIR.getLabel()
                + ","
                + HeaderTrade.AMOUNT.getLabel()
                + ","
                + HeaderTrade.ORDER_SIDE.getLabel()
                + ","
                + HeaderTrade.ORDER_TYPE.getLabel()
                + ","
                + HeaderTrade.TRADE_ACTION.getLabel()
                + ","
                + HeaderTrade.ORDER_CHANNEL.getLabel()
                + ","
                + HeaderTrade.PRICE.getLabel()
                + ","
                + HeaderTrade.FEE.getLabel()
                + ","
                + HeaderTrade.ASSET_AMOUNT.getLabel()
                + ","
                + HeaderTrade.CREATED_AT.getLabel()
                + ","
                + HeaderTrade.UPDATED_AT.getLabel()
                + ","
                + HeaderTrade.TRADE_TYPE.getLabel();
    }
}
