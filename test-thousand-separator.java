import java.math.BigDecimal;
import java.text.NumberFormat;

public class TestThousandSeparator {
    private static final NumberFormat NUMBER_FORMAT = NumberFormat.getInstance();
    
    /**
     * Format BigDecimal for report with thousand separators while preserving all decimal places
     * @param value BigDecimal value to format
     * @return formatted string with thousand separators
     */
    private static String formatBigDecimalForReport(BigDecimal value) {
        if (value == null) {
            return "";
        }
        
        // stripTrailingZeros() 末尾0除去
        // toPlainString() 指数表記にならないようにString変換
        String strValue = value.stripTrailingZeros().toPlainString();
        
        // 整数部3桁区切り
        int decimalPointIndex = strValue.indexOf(".");
        if (decimalPointIndex < 0) {
            // 小数点なし
            return NUMBER_FORMAT.format(Long.valueOf(strValue));
        } else {
            // 小数点あり
            String seisu = strValue.substring(0, decimalPointIndex);
            return NUMBER_FORMAT.format(Long.valueOf(seisu)) + strValue.substring(decimalPointIndex);
        }
    }
    
    public static void main(String[] args) {
        // Test cases
        System.out.println("Testing thousand separator formatting:");
        
        // Test case 1: Integer value
        BigDecimal test1 = new BigDecimal("12345");
        System.out.println("12345 -> " + formatBigDecimalForReport(test1));
        
        // Test case 2: Decimal value
        BigDecimal test2 = new BigDecimal("12345.6789");
        System.out.println("12345.6789 -> " + formatBigDecimalForReport(test2));
        
        // Test case 3: Large number
        BigDecimal test3 = new BigDecimal("1234567890.123456");
        System.out.println("1234567890.123456 -> " + formatBigDecimalForReport(test3));
        
        // Test case 4: Small decimal
        BigDecimal test4 = new BigDecimal("0.123456");
        System.out.println("0.123456 -> " + formatBigDecimalForReport(test4));
        
        // Test case 5: Trailing zeros (should be removed)
        BigDecimal test5 = new BigDecimal("12345.67800");
        System.out.println("12345.67800 -> " + formatBigDecimalForReport(test5));
        
        // Test case 6: Zero
        BigDecimal test6 = new BigDecimal("0");
        System.out.println("0 -> " + formatBigDecimalForReport(test6));
        
        // Test case 7: Null
        System.out.println("null -> '" + formatBigDecimalForReport(null) + "'");
    }
}
